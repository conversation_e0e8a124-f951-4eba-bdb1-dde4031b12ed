'use client';

import * as React from "react";
import { Suspense, lazy, useEffect } from "react";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download, ZoomIn, ZoomOut, X, Maximize2, RotateCw, FileText, Loader2 } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { usePdfHandler } from "@/hooks/use-pdf-handler";
import { useViewerMode } from "@/hooks/use-viewer-mode";
import { useRouter } from "next/navigation";

// Lazy load PDF viewer for better code splitting
const PDFViewer = lazy(() => import("./pdf-viewer").then(module => ({ default: module.PDFViewer })));

interface ImageViewerProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  alt: string;
}

const ImageViewer = ({ isOpen, onClose, imageUrl, alt }: ImageViewerProps) => {
  const isMobile = useIsMobile();
  const { isPdf, handleDownload, isDownloading } = usePdfHandler();
  const { enterViewerMode, exitViewerMode } = useViewerMode();
  const router = useRouter();
  const [scale, setScale] = React.useState(1);
  const [isDragging, setIsDragging] = React.useState(false);
  const [position, setPosition] = React.useState({ x: 0, y: 0 });
  const [startPos, setStartPos] = React.useState({ x: 0, y: 0 });
  const [touchDistance, setTouchDistance] = React.useState<number | null>(null);
  const [initialScale, setInitialScale] = React.useState(1);
  const [rotation, setRotation] = React.useState(0);

  // Check if the current file is a PDF
  const isCurrentPdf = isPdf(imageUrl);

  // إدارة حالة العارض لإخفاء Header و Footer
  useEffect(() => {
    if (isOpen) {
      enterViewerMode();
    } else {
      exitViewerMode();
    }
  }, [isOpen, enterViewerMode, exitViewerMode]);

  // معالجة زر الرجوع في المتصفح مع منع التراكم
  useEffect(() => {
    if (!isOpen) return

    // التحقق من وجود مودال مفتوح بالفعل
    const isModalAlreadyOpen = (window as any).__modalHistoryAdded;

    // حفظ الحالة الأصلية فقط إذا لم يكن هناك مودال مفتوح
    if (!isModalAlreadyOpen) {
      const originalState = window.history.state;
      const originalUrl = window.location.href;

      // تعيين المتغير العالمي
      (window as any).__modalHistoryAdded = true;
      (window as any).__originalState = originalState;
      (window as any).__originalUrl = originalUrl;

      // إضافة حالة للمودال
      window.history.pushState({ modalOpen: true }, '', window.location.href);
    }

    // معالجة زر الرجوع
    const handlePopState = () => {
      onClose();
    }

    // الاستماع لأحداث الرجوع
    window.addEventListener('popstate', handlePopState)

    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [isOpen, onClose])

  // تنظيف التاريخ عند إغلاق المودال
  useEffect(() => {
    if (!isOpen && (window as any).__modalHistoryAdded) {
      // استعادة الحالة الأصلية
      const originalState = (window as any).__originalState;
      const originalUrl = (window as any).__originalUrl;

      window.history.replaceState(originalState, '', originalUrl || window.location.href);

      // تنظيف المتغيرات العالمية
      (window as any).__modalHistoryAdded = false;
      (window as any).__originalState = null;
      (window as any).__originalUrl = null;
    }
  }, [isOpen])

  // معالجة مفتاح Escape
  useEffect(() => {
    if (!isOpen) return

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  // Reset scale and position when opening a new image
  React.useEffect(() => {
    if (isOpen) {
      setScale(1);
      setPosition({ x: 0, y: 0 });
      setRotation(0);
    }
  }, [isOpen, imageUrl]);

  // Helper function to constrain position within bounds
  const constrainPosition = (pos: { x: number; y: number }, currentScale: number) => {
    if (currentScale <= 1) {
      return { x: 0, y: 0 };
    }

    // Calculate maximum allowed movement based on scale and viewport
    // More generous bounds to ensure all content is accessible
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Calculate how much the image extends beyond the viewport when scaled
    const scaledExcess = currentScale - 1;

    // Allow movement proportional to the scaled size
    const maxOffsetX = Math.min(
      (viewportWidth * scaledExcess) / 2 + 200, // Base on viewport width + buffer
      1200 // Maximum limit
    );
    const maxOffsetY = Math.min(
      (viewportHeight * scaledExcess) / 2 + 150, // Base on viewport height + buffer
      800 // Maximum limit
    );

    return {
      x: Math.max(-maxOffsetX, Math.min(maxOffsetX, pos.x)),
      y: Math.max(-maxOffsetY, Math.min(maxOffsetY, pos.y))
    };
  };

  // Zoom in function
  const zoomIn = () => {
    const newScale = Math.min(scale + 0.25, 5);

    // Keep current position but constrain it to new scale bounds
    const constrainedPosition = constrainPosition(position, newScale);
    setPosition(constrainedPosition);
    setScale(newScale);
  };

  // Zoom out function
  const zoomOut = () => {
    const newScale = Math.max(scale - 0.25, 0.5);

    // Keep current position but constrain it to new scale bounds
    const constrainedPosition = constrainPosition(position, newScale);
    setPosition(constrainedPosition);
    setScale(newScale);
  };

  // Reset zoom and position
  const resetView = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
    setRotation(0);
  };

  // Rotate image
  const rotateImage = () => {
    setRotation((prev) => (prev + 90) % 360);
    // Reset position when rotating
    setPosition({ x: 0, y: 0 });
  };

  // Mouse/touch event handlers for dragging
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (scale > 1) {
      setIsDragging(true);
      setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging && scale > 1) {
      const newPosition = {
        x: e.clientX - startPos.x,
        y: e.clientY - startPos.y
      };
      const constrainedPosition = constrainPosition(newPosition, scale);
      setPosition(constrainedPosition);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  // Double click to reset or zoom
  const handleDoubleClick = () => {
    if (scale > 1) {
      // If already zoomed in, reset to normal view
      resetView();
    } else {
      // Zoom in to 2x and keep the image centered
      setScale(2);
      setPosition({ x: 0, y: 0 });
    }
  };

  // Handle mouse wheel for zooming
  const handleWheel = (e: React.WheelEvent<HTMLDivElement>) => {
    e.preventDefault();

    // Determine zoom direction
    const delta = e.deltaY < 0 ? 0.1 : -0.1;
    const newScale = Math.max(0.5, Math.min(5, scale + delta));

    if (newScale !== scale) {
      // Keep current position but constrain it to new scale bounds
      const constrainedPosition = constrainPosition(position, newScale);
      setPosition(constrainedPosition);
      setScale(newScale);
    }
  };

  // Touch event handlers for mobile devices
  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    if (e.touches.length === 1) {
      // Single touch - start dragging
      setIsDragging(true);
      const touch = e.touches[0];
      setStartPos({ x: touch.clientX - position.x, y: touch.clientY - position.y });
    } else if (e.touches.length === 2) {
      // Pinch to zoom - calculate initial distance between two fingers
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.hypot(
        touch2.clientX - touch1.clientX,
        touch2.clientY - touch1.clientY
      );
      setTouchDistance(distance);
      setInitialScale(scale);
    }
  };

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    e.preventDefault(); // Prevent scrolling while interacting with image

    if (e.touches.length === 1 && isDragging && scale > 1) {
      // Single touch - drag the image
      const touch = e.touches[0];
      const newPosition = {
        x: touch.clientX - startPos.x,
        y: touch.clientY - startPos.y
      };
      const constrainedPosition = constrainPosition(newPosition, scale);
      setPosition(constrainedPosition);
    } else if (e.touches.length === 2 && touchDistance !== null) {
      // Pinch to zoom
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const newDistance = Math.hypot(
        touch2.clientX - touch1.clientX,
        touch2.clientY - touch1.clientY
      );

      // Calculate new scale based on distance change
      const distanceRatio = newDistance / touchDistance;
      const newScale = Math.max(0.5, Math.min(5, initialScale * distanceRatio));

      // Keep current position but constrain it to new scale bounds
      const constrainedPosition = constrainPosition(position, newScale);
      setPosition(constrainedPosition);
      setScale(newScale);
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    setTouchDistance(null);

    // Reset position if scale is back to 1 or less
    if (scale <= 1) {
      setPosition({ x: 0, y: 0 });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-[100vw] max-h-[100vh] w-screen h-screen p-0 m-0 rounded-none overflow-hidden bg-background/95 backdrop-blur-sm border-none">
        <div className="relative w-full h-full flex flex-col">
          {/* Header with controls */}
          <div className="flex justify-between items-center p-3 bg-background/80 border-b border-border z-10">
            <div className="flex gap-2">
              {!isCurrentPdf && (
                <>
                  <Button variant="ghost" size="sm" onClick={zoomOut} title="تصغير" className="h-9 w-9">
                    <ZoomOut className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={zoomIn} title="تكبير" className="h-9 w-9">
                    <ZoomIn className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={resetView} title="إعادة ضبط" className="h-9 w-9">
                    <Maximize2 className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={rotateImage} title="تدوير الصورة" className="h-9 w-9">
                    <RotateCw className="h-5 w-5" />
                  </Button>
                </>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDownload(imageUrl)}
                disabled={isDownloading}
                title={isDownloading ? "جاري التحميل..." : (isCurrentPdf ? "تحميل ملف PDF" : "تحميل الصورة")}
                className="h-9 px-3 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isDownloading ? (
                  <Loader2 className="h-5 w-5 ml-1 animate-spin" />
                ) : (
                  <Download className="h-5 w-5 ml-1" />
                )}
                <span>{isDownloading ? "جاري التحميل..." : "تحميل"}</span>
              </Button>
            </div>
            <div className="text-sm text-muted-foreground flex items-center gap-2">
              {isCurrentPdf && <FileText className="h-4 w-4 mr-1" />}
              {!isCurrentPdf && rotation > 0 && <RotateCw className="h-3 w-3" />}
              {!isCurrentPdf && scale !== 1 && `${Math.round(scale * 100)}%`}
              {!isCurrentPdf && rotation > 0 && `${rotation}°`}
              {!isCurrentPdf && scale > 1 && (
                <span className="text-xs opacity-70">اسحب للتحريك</span>
              )}
            </div>
            <DialogClose asChild>
              <Button variant="ghost" size="sm" title="إغلاق" className="h-9 w-9">
                <X className="h-5 w-5" />
              </Button>
            </DialogClose>
          </div>

          {/* Image/PDF container - takes full remaining height */}
          <div
            className={`flex-1 overflow-hidden p-0 flex items-center justify-center ${isCurrentPdf ? 'bg-muted/10' : 'bg-black/90'} ${!isCurrentPdf ? 'cursor-grab active:cursor-grabbing touch-none' : ''}`}
            onMouseDown={!isCurrentPdf ? handleMouseDown : undefined}
            onMouseMove={!isCurrentPdf ? handleMouseMove : undefined}
            onMouseUp={!isCurrentPdf ? handleMouseUp : undefined}
            onMouseLeave={!isCurrentPdf ? handleMouseLeave : undefined}
            onDoubleClick={!isCurrentPdf ? handleDoubleClick : undefined}
            onWheel={!isCurrentPdf ? handleWheel : undefined}
            onTouchStart={!isCurrentPdf ? handleTouchStart : undefined}
            onTouchMove={!isCurrentPdf ? handleTouchMove : undefined}
            onTouchEnd={!isCurrentPdf ? handleTouchEnd : undefined}
            onTouchCancel={!isCurrentPdf ? handleTouchEnd : undefined}
          >
            <div
              className="relative transition-transform duration-100 ease-out flex items-center justify-center w-full h-full"
              style={{
                cursor: !isCurrentPdf && scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default',
                transition: isDragging ? 'none' : 'transform 0.2s ease-out'
              }}
            >
              <div
                className="flex items-center justify-center"
                style={{
                  transform: isCurrentPdf ? 'none' : `translate(${position.x}px, ${position.y}px) rotate(${rotation}deg) scale(${scale})`,
                  transition: isDragging ? 'none' : 'transform 0.2s ease-out',
                  width: '100%',
                  height: '100%'
                }}
              >
                {isCurrentPdf ? (
                  <div className="w-full h-full">
                    {/* استخدام مكون PDF Viewer المحسن لجميع الأجهزة */}
                    <Suspense fallback={
                      <div className="flex items-center justify-center h-full bg-background/90">
                        <div className="flex flex-col items-center gap-3">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                          <p className="text-sm text-muted-foreground">جاري تحميل PDF...</p>
                        </div>
                      </div>
                    }>
                      <PDFViewer
                        url={imageUrl}
                        title={alt}
                        onDownload={handleDownload}
                        isMobile={isMobile}
                      />
                    </Suspense>
                  </div>
                ) : (
                  <div className="w-full h-full">
                    {/* عرض الصورة بملء الشاشة بالكامل مثل PDF */}
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={imageUrl}
                      alt={alt}
                      className="w-full h-full object-contain select-none"
                      draggable="false"
                      style={{
                        minHeight: '100%',
                        width: '100%',
                        border: 'none',
                        outline: 'none',
                        display: 'block'
                      }}
                      onError={(e) => {
                        console.error("فشل تحميل الصورة في العارض:", imageUrl);
                        (e.target as HTMLImageElement).src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='18' height='18' x='3' y='3' rx='2' ry='2'/%3E%3Ccircle cx='9' cy='9' r='2'/%3E%3Cpath d='m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21'/%3E%3C/svg%3E";
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export { ImageViewer };
