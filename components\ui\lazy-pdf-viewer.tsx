'use client'

import { lazy, Suspense, useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { FileText, Download, Loader2, X } from 'lucide-react'
import { useViewerMode } from '@/hooks/use-viewer-mode'
import { useRouter } from 'next/navigation'

// تحميل ديناميكي لـ PDF Viewer
const PDFViewer = lazy(() => 
  import('./pdf-viewer').then(module => ({ default: module.PDFViewer }))
)

interface LazyPDFViewerProps {
  url: string
  title?: string
  className?: string
}

// مكون Loading للـ PDF
function PDFLoadingFallback() {
  return (
    <div className="flex flex-col items-center justify-center p-8 bg-muted/50 rounded-lg min-h-[400px]">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-sm text-muted-foreground">جاري تحميل عارض PDF...</p>
    </div>
  )
}

// مكون خطأ للـ PDF
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function PDFErrorFallback({ url, title }: { url: string; title?: string }) {
  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = url
    link.download = title || 'document.pdf'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="flex flex-col items-center justify-center p-8 bg-muted/50 rounded-lg min-h-[400px]">
      <FileText className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-semibold mb-2">لا يمكن عرض PDF</h3>
      <p className="text-sm text-muted-foreground mb-4 text-center">
        عذراً، لا يمكن عرض ملف PDF في المتصفح. يمكنك تحميله لعرضه.
      </p>
      <Button onClick={handleDownload} variant="outline">
        <Download className="h-4 w-4 mr-2" />
        تحميل PDF
      </Button>
    </div>
  )
}

export function LazyPDFViewer({ url, title, className }: LazyPDFViewerProps) {
  const [showViewer, setShowViewer] = useState(false)

  // إذا لم يتم تحميل العارض بعد، اعرض زر التحميل
  if (!showViewer) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-muted/50 rounded-lg min-h-[400px]">
        <FileText className="h-12 w-12 text-primary mb-4" />
        <h3 className="text-lg font-semibold mb-2">عرض PDF</h3>
        <p className="text-sm text-muted-foreground mb-4 text-center">
          انقر لتحميل وعرض ملف PDF
        </p>
        <Button
          onClick={() => setShowViewer(true)}
          className="mb-2"
        >
          <FileText className="h-4 w-4 mr-2" />
          عرض PDF
        </Button>
        <Button
          variant="outline"
          onClick={() => {
            const link = document.createElement('a')
            link.href = url
            link.download = title || 'document.pdf'
            link.target = '_blank'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }}
        >
          <Download className="h-4 w-4 mr-2" />
          تحميل فقط
        </Button>
      </div>
    )
  }

  return (
    <div className={className}>
      <Suspense fallback={<PDFLoadingFallback />}>
        <PDFViewer
          url={url}
          title={title}
        />
      </Suspense>
    </div>
  )
}

// مكون مبسط لعرض PDF في modal
export function PDFModal({
  url,
  title,
  isOpen,
  onClose
}: {
  url: string
  title?: string
  isOpen: boolean
  onClose: () => void
}) {
  const { enterViewerMode, exitViewerMode } = useViewerMode();
  const router = useRouter();

  // إدارة حالة العارض لإخفاء Header و Footer
  useEffect(() => {
    if (isOpen) {
      enterViewerMode();
    } else {
      exitViewerMode();
    }
  }, [isOpen, enterViewerMode, exitViewerMode]);

  // معالجة زر الرجوع في المتصفح مع منع التراكم
  useEffect(() => {
    if (!isOpen) return

    // التحقق من وجود مودال مفتوح بالفعل
    const isModalAlreadyOpen = (window as any).__modalHistoryAdded;

    // حفظ الحالة الأصلية فقط إذا لم يكن هناك مودال مفتوح
    if (!isModalAlreadyOpen) {
      const originalState = window.history.state;
      const originalUrl = window.location.href;

      // تعيين المتغير العالمي
      (window as any).__modalHistoryAdded = true;
      (window as any).__originalState = originalState;
      (window as any).__originalUrl = originalUrl;

      // إضافة حالة للمودال
      window.history.pushState({ modalOpen: true }, '', window.location.href);
    }

    // معالجة زر الرجوع
    const handlePopState = () => {
      onClose();
    }

    // الاستماع لأحداث الرجوع
    window.addEventListener('popstate', handlePopState)

    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [isOpen, onClose])

  // تنظيف التاريخ عند إغلاق المودال
  useEffect(() => {
    if (!isOpen && (window as any).__modalHistoryAdded) {
      // استعادة الحالة الأصلية
      const originalState = (window as any).__originalState;
      const originalUrl = (window as any).__originalUrl;

      window.history.replaceState(originalState, '', originalUrl || window.location.href);

      // تنظيف المتغيرات العالمية
      (window as any).__modalHistoryAdded = false;
      (window as any).__originalState = null;
      (window as any).__originalUrl = null;
    }
  }, [isOpen])

  // معالجة مفتاح Escape
  useEffect(() => {
    if (!isOpen) return

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/95 flex items-center justify-center">
      {/* عرض PDF بملء الشاشة */}
      <div className="bg-background w-screen h-screen flex flex-col">
        {/* شريط التحكم العلوي */}
        <div className="flex items-center justify-between p-3 bg-background/90 border-b border-border backdrop-blur-sm z-10">
          <h2 className="text-lg font-semibold text-foreground truncate flex-1 mr-4">
            {title || 'عرض PDF'}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-destructive hover:text-destructive-foreground"
            title="إغلاق (Esc)"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* منطقة عرض PDF */}
        <div className="flex-1 overflow-hidden bg-muted/20">
          <LazyPDFViewer url={url} title={title} className="h-full w-full" />
        </div>
      </div>
    </div>
  )
}

// Hook لإدارة حالة PDF Modal
export function usePDFModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [pdfUrl, setPdfUrl] = useState('')
  const [pdfTitle, setPdfTitle] = useState('')

  const openPDF = (url: string, title?: string) => {
    setPdfUrl(url)
    setPdfTitle(title || '')
    setIsOpen(true)
  }

  const closePDF = () => {
    setIsOpen(false)
    setPdfUrl('')
    setPdfTitle('')
  }

  return {
    isOpen,
    pdfUrl,
    pdfTitle,
    openPDF,
    closePDF
  }
}
